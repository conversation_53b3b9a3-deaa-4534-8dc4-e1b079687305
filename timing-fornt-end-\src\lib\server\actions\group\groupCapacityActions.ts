'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'

export interface GroupCapacityStats {
    total_groups: number;
    total_students: number;
    average_per_group: number;
    groups: Array<{
        id: number;
        number: number;
        student_count: number;
    }>;
}

export async function getGroupCapacityStats(sectionId: number): Promise<GroupCapacityStats> {
    try {
        const { data } = await axiosInstance.get<GroupCapacityStats>(
            `/sections/${sectionId}/group-capacity`
        )
        return data
    } catch (error: any) {
        console.error('Error fetching group capacity stats:', error.response?.data)
        throw error
    }
}

export async function refreshGroupData(): Promise<void> {
    try {
        // Revalidate all group-related pages
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/students')
        revalidatePath('/dashboard')
    } catch (error) {
        console.error('Error refreshing group data:', error)
        throw error
    }
}

export async function forceRefreshGroupCounts(): Promise<{ success: boolean }> {
    try {
        // This function can be called to force refresh group counts
        // It revalidates all relevant paths
        revalidatePath('/dashboard/groups', 'page')
        revalidatePath('/dashboard/students', 'page')
        revalidatePath('/dashboard', 'page')
        
        return { success: true }
    } catch (error) {
        console.error('Error force refreshing group counts:', error)
        throw error
    }
}
