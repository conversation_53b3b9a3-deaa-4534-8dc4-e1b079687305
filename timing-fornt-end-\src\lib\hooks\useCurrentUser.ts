'use client'

import { useState, useEffect } from 'react'
import { getUser } from '@/lib/server/actions/auth/getUser'

interface CurrentUser {
    id: number
    email: string
    key: {
        keyable_type: string
        keyable: {
            id: number
            name: string
            last: string
            username: string
        }
    }
}

export function useCurrentUser() {
    const [user, setUser] = useState<CurrentUser | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        const fetchUser = async () => {
            try {
                const result = await getUser()
                if (result.user) {
                    setUser(result.user as CurrentUser)
                } else {
                    setError(result.error || 'Failed to get user')
                }
            } catch (err) {
                setError('Failed to fetch user')
                console.error('Error fetching user:', err)
            } finally {
                setLoading(false)
            }
        }

        fetchUser()
    }, [])

    return { user, loading, error }
}
