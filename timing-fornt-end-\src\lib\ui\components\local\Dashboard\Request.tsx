"use client";
import { <PERSON>, <PERSON>, X, Clock, MessageSquare } from "lucide-react"
import Modal, { openModal } from "../../global/Modal/Modal"
import { useEffect, useState } from "react"
import { getRequests, updateRequestStatus, getPendingRequestsCount } from "@/lib/server/actions/request/requestActions"
import type { RequestResponse } from "@/lib/server/actions/request/requestActions"
import { useCurrentUser } from "@/lib/hooks/useCurrentUser"
import Button from "../../global/Buttons/Button"

export default function Request() {
    const { user, loading: userLoading } = useCurrentUser()
    const [requests, setRequests] = useState<RequestResponse[]>([])
    const [pendingCount, setPendingCount] = useState(0)
    const [loading, setLoading] = useState(false)
    const [adminResponses, setAdminResponses] = useState<{[key: number]: string}>({})
    const [processingRequest, setProcessingRequest] = useState<number | null>(null)
    const [successMessage, setSuccessMessage] = useState<string | null>(null)

    const fetchRequests = async () => {
        try {
            setLoading(true)
            const [requestsData, countData] = await Promise.all([
                getRequests(1, 'pending'),
                getPendingRequestsCount()
            ])
            console.log('Requests data:', requestsData.data) // Debug log
            console.log('Pending count:', countData) // Debug log
            setRequests(requestsData.data)
            setPendingCount(countData)
        } catch (error) {
            console.error('Error fetching requests:', error)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        // Only fetch requests if user is an admin
        if (user && user.key.keyable_type === 'admin') {
            fetchRequests()
        }
    }, [user])

    const handleRequestAction = async (requestId: number, status: 'approved' | 'rejected') => {
        try {
            setProcessingRequest(requestId)
            const adminResponse = adminResponses[requestId] || ''

            const result = await updateRequestStatus(requestId, status, adminResponse)

            if (result.success) {
                setSuccessMessage(`Request ${status} successfully! Teacher has been notified.`)
                setTimeout(() => setSuccessMessage(null), 3000)
            }

            await fetchRequests() // Refresh the list

            // Clear the admin response for this request
            setAdminResponses(prev => {
                const newResponses = { ...prev }
                delete newResponses[requestId]
                return newResponses
            })
        } catch (error) {
            console.error('Error updating request:', error)
        } finally {
            setProcessingRequest(null)
        }
    }

    const handleAdminResponseChange = (requestId: number, response: string) => {
        setAdminResponses(prev => ({
            ...prev,
            [requestId]: response
        }))
    }

    // Only show admin requests if user is actually an admin
    if (userLoading) {
        return (
            <div className="relative">
                <Bell className="text-gray-400" size={24} />
            </div>
        )
    }

    if (!user || user.key.keyable_type !== 'admin') {
        return null // Don't show admin requests for non-admins
    }

    return (
        <>
            <div className="relative">
                <Bell
                    className="text-primary dark:text-dark-primary cursor-pointer"
                    size={24}
                    onClick={() => openModal("request-modal")}
                />
                {pendingCount > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {pendingCount}
                    </span>
                )}
            </div>
            <Modal id="request-modal">
                <div className="flex flex-col gap-4 h-[60vh] w-2/3 max-w-4xl">
                    <h2 className="text-xl font-bold text-on-surface dark:text-dark-on-surface">
                        Teacher Requests ({pendingCount} pending)
                    </h2>

                    {successMessage && (
                        <div className="flex items-center gap-2 p-3 bg-green-100 border border-green-300 rounded-lg text-green-700">
                            <Check size={20} />
                            <span>{successMessage}</span>
                        </div>
                    )}

                    {loading ? (
                        <div className="flex items-center justify-center h-32">
                            <Clock className="animate-spin" size={24} />
                            <span className="ml-2">Loading requests...</span>
                        </div>
                    ) : requests.length === 0 ? (
                        <div className="flex items-center justify-center h-32 text-gray-500">
                            No pending requests
                        </div>
                    ) : (
                        <ul className="flex flex-col gap-4 overflow-y-auto p-4 border rounded-lg">
                            {requests.map((request) => (
                                <li key={request.id} className="flex flex-col gap-2 p-4 bg-surface-container dark:bg-dark-surface-container rounded-lg shadow">
                                    <div className="flex justify-between items-start">
                                        <div className="flex flex-col gap-1">
                                            <h3 className="font-semibold text-lg">
                                                {request.teacher?.name || 'Unknown'} {request.teacher?.last || 'Teacher'}
                                            </h3>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                @{request.teacher?.username || 'unknown'}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <span className="text-sm text-primary-container dark:text-dark-primary-container">
                                                {request.start_time || 'N/A'} - {request.end_time || 'N/A'}
                                            </span>
                                            <p className="text-xs text-gray-500">
                                                {request.type?.toUpperCase() || 'N/A'} • {request.day || 'N/A'}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span className="font-medium">Module:</span> {request.module?.name || 'N/A'}
                                        </div>
                                        <div>
                                            <span className="font-medium">Classroom:</span> {request.classRome?.number || 'N/A'}
                                        </div>
                                        {request.section && (
                                            <div>
                                                <span className="font-medium">Section:</span> {request.section.name}
                                            </div>
                                        )}
                                        {request.group && (
                                            <div>
                                                <span className="font-medium">Group:</span> {request.group.number}
                                            </div>
                                        )}
                                    </div>

                                    {request.message && (
                                        <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                                            <span className="font-medium">Teacher Message:</span> {request.message}
                                        </div>
                                    )}

                                    <div className="mt-3">
                                        <label className="block text-sm font-medium mb-2">
                                            Admin Response (Optional):
                                        </label>
                                        <textarea
                                            className="w-full p-2 border rounded-md text-sm resize-none"
                                            rows={2}
                                            placeholder="Add a response message for the teacher..."
                                            value={adminResponses[request.id] || ''}
                                            onChange={(e) => handleAdminResponseChange(request.id, e.target.value)}
                                        />
                                    </div>

                                    <div className="flex gap-2 mt-3">
                                        <Button
                                            mode="filled"
                                            icon={processingRequest === request.id ? <Clock className="animate-spin" size={16} /> : <Check size={16} />}
                                            onClick={() => handleRequestAction(request.id, 'approved')}
                                            disabled={processingRequest === request.id}
                                        >
                                            {processingRequest === request.id ? 'Processing...' : 'Approve'}
                                        </Button>
                                        <Button
                                            mode="outlined"
                                            icon={processingRequest === request.id ? <Clock className="animate-spin" size={16} /> : <X size={16} />}
                                            onClick={() => handleRequestAction(request.id, 'rejected')}
                                            disabled={processingRequest === request.id}
                                        >
                                            {processingRequest === request.id ? 'Processing...' : 'Reject'}
                                        </Button>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </Modal>
        </>
    )
}