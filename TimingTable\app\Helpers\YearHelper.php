<?php

namespace App\Helpers;

class YearHelper
{
    /**
     * Convert year level to academic naming convention
     * 
     * @param int $yearLevel The year level (1-5)
     * @return string The academic name (L1, L2, L3, M1, M2)
     */
    public static function getAcademicName(int $yearLevel): string
    {
        return match($yearLevel) {
            1 => 'L1',
            2 => 'L2', 
            3 => 'L3',
            4 => 'M1',
            5 => 'M2',
            default => "Year {$yearLevel}"
        };
    }

    /**
     * Get year level from academic name
     * 
     * @param string $academicName The academic name (L1, L2, L3, M1, M2)
     * @return int|null The year level or null if not found
     */
    public static function getYearLevel(string $academicName): ?int
    {
        return match($academicName) {
            'L1' => 1,
            'L2' => 2,
            'L3' => 3,
            'M1' => 4,
            'M2' => 5,
            default => null
        };
    }

    /**
     * Check if a year level is a License year (1-3)
     * 
     * @param int $yearLevel
     * @return bool
     */
    public static function isLicenseYear(int $yearLevel): bool
    {
        return $yearLevel >= 1 && $yearLevel <= 3;
    }

    /**
     * Check if a year level is a Master year (4-5)
     * 
     * @param int $yearLevel
     * @return bool
     */
    public static function isMasterYear(int $yearLevel): bool
    {
        return $yearLevel >= 4 && $yearLevel <= 5;
    }

    /**
     * Get all valid year mappings
     * 
     * @return array
     */
    public static function getAllYearMappings(): array
    {
        return [
            1 => 'L1',
            2 => 'L2',
            3 => 'L3',
            4 => 'M1',
            5 => 'M2'
        ];
    }

    /**
     * Validate year level
     * 
     * @param int $yearLevel
     * @return bool
     */
    public static function isValidYearLevel(int $yearLevel): bool
    {
        return $yearLevel >= 1 && $yearLevel <= 5;
    }
}
