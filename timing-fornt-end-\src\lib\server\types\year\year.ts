export interface Year {
    id: number;
    name: string;
    year: number;
    department_id: number;
    created_at?: string;
    updated_at?: string;
}

export interface CreateYearRequest {
    name: string;
    year: number;
    department_id: number;
}

export interface YearErrorResponse {
    message: string;
}

export interface YearResponse {
    years: Year[];
    pagination?: {
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
}
