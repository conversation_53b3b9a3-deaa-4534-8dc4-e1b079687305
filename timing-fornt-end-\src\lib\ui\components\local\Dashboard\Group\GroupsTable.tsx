import { DashContentTable, TableTd, TableTdMain, TableThead, TableTr } from "../DashCrudContent";
import GroupActions from "@/lib/ui/forms/group/actions";
import CreateGroupKey from "@/lib/ui/forms/group/createKey";
import { GroupResponse } from "@/lib/server/types/group/group";

interface GroupsTableProps {
    groups: GroupResponse;
}

export default function GroupsTable({ groups }: GroupsTableProps) {

    return (
        <>
            <DashContentTable>
                <TableThead list={['Number', 'Section', 'Year', 'Department', 'Students', 'Settings']} />
                <tbody>
                    {groups?.data.map((group) => (
                        <TableTr key={group.id}>
                            <TableTdMain value={group.number.toString()} />
                            <TableTd>
                                {group.section.number}
                            </TableTd>
                            <TableTd>
                                {group.section.year.name}
                            </TableTd>
                            <TableTd>
                                {group.section.year.department.name}
                            </TableTd>
                            <TableTd>
                                <div className="flex items-center gap-2">
                                    <span className="font-medium">{group.students_count}</span>
                                    {group.students_count > 25 && (
                                        <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 rounded-full">
                                            High
                                        </span>
                                    )}
                                    {group.students_count >= 30 && (
                                        <span className="px-2 py-1 text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full">
                                            Full
                                        </span>
                                    )}
                                    {group.students_count === 0 && (
                                        <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 rounded-full">
                                            Empty
                                        </span>
                                    )}
                                </div>
                            </TableTd>
                            <TableTd>
                                <GroupActions group={group} />
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
        </>
    )
}

