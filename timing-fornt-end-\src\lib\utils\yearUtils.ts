/**
 * Utility functions for year naming convention
 * Handles conversion between year levels and academic naming (L1-L3, M1-M2)
 */

/**
 * Convert year level to academic naming convention
 * @param yearLevel The year level (1-5)
 * @returns The academic name (L1, L2, L3, M1, M2)
 */
export function getAcademicName(yearLevel: number): string {
  switch (yearLevel) {
    case 1: return 'L1';
    case 2: return 'L2';
    case 3: return 'L3';
    case 4: return 'M1';
    case 5: return 'M2';
    default: return `Year ${yearLevel}`;
  }
}

/**
 * Get year level from academic name
 * @param academicName The academic name (L1, L2, L3, M1, M2)
 * @returns The year level or null if not found
 */
export function getYearLevel(academicName: string): number | null {
  switch (academicName) {
    case 'L1': return 1;
    case 'L2': return 2;
    case 'L3': return 3;
    case 'M1': return 4;
    case 'M2': return 5;
    default: return null;
  }
}

/**
 * Check if a year level is a License year (1-3)
 * @param yearLevel The year level
 * @returns True if it's a License year
 */
export function isLicenseYear(yearLevel: number): boolean {
  return yearLevel >= 1 && yearLevel <= 3;
}

/**
 * Check if a year level is a Master year (4-5)
 * @param yearLevel The year level
 * @returns True if it's a Master year
 */
export function isMasterYear(yearLevel: number): boolean {
  return yearLevel >= 4 && yearLevel <= 5;
}

/**
 * Get all valid year mappings
 * @returns Object mapping year levels to academic names
 */
export function getAllYearMappings(): Record<number, string> {
  return {
    1: 'L1',
    2: 'L2',
    3: 'L3',
    4: 'M1',
    5: 'M2'
  };
}

/**
 * Validate year level
 * @param yearLevel The year level to validate
 * @returns True if the year level is valid (1-5)
 */
export function isValidYearLevel(yearLevel: number): boolean {
  return yearLevel >= 1 && yearLevel <= 5;
}

/**
 * Get year type description
 * @param yearLevel The year level
 * @returns Description of the year type
 */
export function getYearTypeDescription(yearLevel: number): string {
  if (isLicenseYear(yearLevel)) {
    return 'License';
  } else if (isMasterYear(yearLevel)) {
    return 'Master';
  }
  return 'Unknown';
}

/**
 * Format year display name with type
 * @param yearLevel The year level
 * @param specialization Optional specialization name
 * @returns Formatted display name
 */
export function formatYearDisplayName(yearLevel: number, specialization?: string): string {
  const academicName = getAcademicName(yearLevel);
  return specialization ? `${academicName} ${specialization}` : academicName;
}
