<?php

namespace App\Http\Controllers\Api\Users;

use App\Http\Controllers\Controller;
use App\Models\Api\Users\Student;
use App\Models\Api\Main\Group;
use App\Models\Api\Core\Baladiya;
use Illuminate\Http\Request;

class StudentsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $students = Student::with(['key.user', 'group.section.year.department'])
            ->when($request->has('search'), function ($query) use ($request) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%')
                      ->orWhere('last', 'like', '%' . $search . '%')
                      ->orWhere('username', 'like', '%' . $search . '%');
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(6);

        return response()->json($students);
    }

    public function timeTable(Student $student)
    {
        return true;
    }


    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'last' => 'required|string|max:255',
                'date_of_birth' => 'required|date|before:today',
                'inscreption_number' => 'required|string|unique:students,inscreption_number|max:255',
                'section_id' => 'required|exists:sections,id',
                'baladiyas_id' => 'nullable|exists:baladiyas,id',
            ]);

            // Automatically assign to the most appropriate group in the section
            $groupId = $this->assignToOptimalGroup($validated['section_id']);

            if (!$groupId) {
                return response()->json([
                    'message' => 'No available groups found in the selected section. Please create a group first.',
                    'errors' => ['section_id' => ['No groups available in this section']]
                ], 422);
            }

            // Ensure we have a valid baladiya_id
            if (!isset($validated['baladiyas_id'])) {
                // Get the first available baladiya as default
                $defaultBaladiya = Baladiya::first();
                if (!$defaultBaladiya) {
                    return response()->json([
                        'message' => 'No baladiya found in the system. Please contact administrator.',
                        'errors' => ['baladiyas_id' => ['No baladiya available']]
                    ], 422);
                }
                $validated['baladiyas_id'] = $defaultBaladiya->id;
            }

            $student = Student::create([
                'username' => $validated['name'] . '_' . $validated['last'] . '_' . str()->random(6),
                'name' => $validated['name'],
                'last' => $validated['last'],
                'date_of_birth' => $validated['date_of_birth'],
                'inscreption_number' => $validated['inscreption_number'],
                'baladiyas_id' => $validated['baladiyas_id'],
                'group_id' => $groupId,
            ]);

            return response()->json([
                'message' => 'Student created successfully and assigned to group',
                'student' => $student->load(['key.user', 'group.section.year.department'])
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Student creation error: ' . $e->getMessage());
            return response()->json([
                'message' => 'An error occurred while creating the student: ' . $e->getMessage()
            ], 500);
        }
    }

    public function createKey(Student $student)
    {
        $student->key()->create([
            'value' => str()->random(10),
        ]);

        return response()->json([
            'message' => 'Key created successfully',
            'key' => $student->key->value,
        ], 201);
    }


    /**
     * Display the specified resource.
     */
    public function show(Student $student)
    {
        return response()->json([
            'message' => 'Student fetched successfully',
            'student' => $student->load('key.user')
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Student $student)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'last' => 'required|string',
            'date_of_birth' => 'required|date',
            'inscreption_number' => 'required|string|unique:students,inscreption_number,' . $student->id,
            'section_id' => 'sometimes|exists:sections,id',
            'group_id' => 'sometimes|exists:groups,id',
            'baladiyas_id' => 'nullable|exists:baladiyas,id',
        ]);

        // If section_id is provided but group_id is not, auto-assign to optimal group
        if (isset($validated['section_id']) && !isset($validated['group_id'])) {
            $groupId = $this->assignToOptimalGroup($validated['section_id']);
            if ($groupId) {
                $validated['group_id'] = $groupId;
            }
        }

        $validated['username'] = $validated['name'] . '_' . $validated['last'] . '_' . str()->random(6);
        $student->update($validated);

        return response()->json([
            'message' => 'Student updated successfully',
            'student' => $student->load(['key.user', 'group.section.year.department'])
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Student $student)
    {
        $student->key()->delete();
        $student->delete();
        return response()->json([
            'message' => 'Student deleted successfully'
        ], 200);
    }

    /**
     * Automatically assign student to the optimal group in a section
     * Uses capacity management to distribute students evenly
     */
    private function assignToOptimalGroup($sectionId, $maxCapacityPerGroup = 30)
    {
        // Get all groups in the section with their current student counts
        $groups = Group::where('section_id', $sectionId)
            ->withCount('students')
            ->get();

        if ($groups->isEmpty()) {
            return null;
        }

        // Filter groups that haven't reached maximum capacity
        $availableGroups = $groups->filter(function ($group) use ($maxCapacityPerGroup) {
            return $group->students_count < $maxCapacityPerGroup;
        });

        if ($availableGroups->isEmpty()) {
            // If all groups are at capacity, return the group with least students anyway
            $optimalGroup = $groups->sortBy('students_count')->first();
        } else {
            // Find the group with the least number of students among available groups
            $optimalGroup = $availableGroups->sortBy('students_count')->first();
        }

        return $optimalGroup->id;
    }

    /**
     * Get group capacity statistics for a section
     */
    public function getGroupCapacityStats($sectionId)
    {
        $groups = Group::where('section_id', $sectionId)
            ->withCount('students')
            ->get();

        return response()->json([
            'total_groups' => $groups->count(),
            'total_students' => $groups->sum('students_count'),
            'average_per_group' => $groups->count() > 0 ? round($groups->sum('students_count') / $groups->count(), 2) : 0,
            'groups' => $groups->map(function ($group) {
                return [
                    'id' => $group->id,
                    'number' => $group->number,
                    'student_count' => $group->students_count
                ];
            })
        ]);
    }
}
