"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateSectionForm from "@/lib/ui/forms/section/CreateSectionForm";
import { Plus } from "lucide-react";

export default function CreateSectionDialog() {
    const [open, setOpen] = useState(false);
    const router = useRouter();

    const handleSuccess = () => {
        setOpen(false);
        router.refresh();
    };

    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Section
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Section">
                <CreateSectionForm onSuccess={handleSuccess} />
            </Dialog>
        </>
    );
}