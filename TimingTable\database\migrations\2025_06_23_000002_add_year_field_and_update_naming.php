<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Api\Main\Year;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add the year field to the years table
        Schema::table('years', function (Blueprint $table) {
            $table->integer('year')->after('name')->default(1);
        });

        // Update existing year records to use new naming convention
        $yearMappings = [
            // Map old names to new names and year levels
            '1 License' => ['name' => 'L1', 'year' => 1],
            '2 License Computer Science' => ['name' => 'L2 Computer Science', 'year' => 2],
            '2 License Math' => ['name' => 'L2 Math', 'year' => 2],
            '3 License SI' => ['name' => 'L3 SI', 'year' => 3],
            '3 License ISIL' => ['name' => 'L3 ISIL', 'year' => 3],
            '1 Master AD' => ['name' => 'M1 AD', 'year' => 4],
            '1 Master IA' => ['name' => 'M1 IA', 'year' => 4],
            '2 Master AD' => ['name' => 'M2 AD', 'year' => 5],
            '2 Master IA' => ['name' => 'M2 IA', 'year' => 5],
            // Generic year names from DepartmentSeeder
            'Year 1' => ['name' => 'L1', 'year' => 1],
            'Year 2' => ['name' => 'L2', 'year' => 2],
            'Year 3' => ['name' => 'L3', 'year' => 3],
            'Year 4' => ['name' => 'M1', 'year' => 4],
            'Year 5' => ['name' => 'M2', 'year' => 5],
        ];

        foreach ($yearMappings as $oldName => $newData) {
            Year::where('name', $oldName)->update([
                'name' => $newData['name'],
                'year' => $newData['year']
            ]);
        }

        // Update any remaining years that might have different patterns
        // This handles cases where years might have been created with different naming
        $years = Year::all();
        foreach ($years as $year) {
            if ($year->year == 0 || $year->year == null) {
                // Try to extract year level from name
                if (preg_match('/(\d+)/', $year->name, $matches)) {
                    $yearLevel = (int)$matches[1];
                    if ($yearLevel >= 1 && $yearLevel <= 5) {
                        $newName = $yearLevel <= 3 ? "L{$yearLevel}" : "M" . ($yearLevel - 3);
                        $year->update([
                            'name' => $newName,
                            'year' => $yearLevel
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the year name changes
        $reverseMappings = [
            'L1' => '1 License',
            'L2 Computer Science' => '2 License Computer Science',
            'L2 Math' => '2 License Math',
            'L3 SI' => '3 License SI',
            'L3 ISIL' => '3 License ISIL',
            'M1 AD' => '1 Master AD',
            'M1 IA' => '1 Master IA',
            'M2 AD' => '2 Master AD',
            'M2 IA' => '2 Master IA',
            'L2' => 'Year 2',
            'L3' => 'Year 3',
            'M1' => 'Year 4',
            'M2' => 'Year 5',
        ];

        foreach ($reverseMappings as $newName => $oldName) {
            Year::where('name', $newName)->update(['name' => $oldName]);
        }

        // Remove the year field
        Schema::table('years', function (Blueprint $table) {
            $table->dropColumn('year');
        });
    }
};
