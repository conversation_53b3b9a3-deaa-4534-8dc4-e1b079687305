import TimingTableGroup from "./groupTimng";
import TimingTableSection from "./sectionTimng";
import { getGroups } from "@/lib/server/actions/group/groupActions";
import PrintButton from "./PrintButton";

export default async function Index() {
    const data = await getGroups();
    console.log('Student Timetable Data:', JSON.stringify(data, null, 2));

    if (!data.timeTableGroup || !data.timeTableSection) {
        console.error('Missing timetable data:', data);
        return <div>No timetable data available</div>;
    }

    if (!data.groupInfo || !data.sectionInfo) {
        console.error('Missing group/section info:', data);
        return <div>No group/section information available</div>;
    }

    return (
        <>
            <div id="printable-timetable">
                <TimingTableSection data={data.timeTableSection} section={data.sectionInfo.number} />
                <hr className="h-1 bg-primary dark:bg-dark-primary" />
                <TimingTableGroup data={data.timeTableGroup} group={data.groupInfo.number} />
            </div>
            <div className="flex justify-center mt-6">
                <PrintButton />
            </div>
        </>
    )
}

