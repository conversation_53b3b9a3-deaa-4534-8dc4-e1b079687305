"use client";

import { Department } from "@/lib/server/types/departments/allDepartments";
import { Trash2 } from "lucide-react";
import { deleteDepartment } from "@/lib/server/actions/department/DepartmentActions";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface DeleteDepartmentProps {
    department: Department;
}

export default function DeleteDepartment({ department }: DeleteDepartmentProps) {
    const router = useRouter();
    const [isDeleting, setIsDeleting] = useState(false);
    const [showConfirm, setShowConfirm] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const handleDelete = async () => {
        try {
            setIsDeleting(true);
            setError(null);
            await deleteDepartment(department.id);
            router.refresh();
        } catch (error: any) {
            console.error('Error deleting department:', error);
            const errorMessage = error?.response?.data?.message || error?.message || 'Error deleting department. Please try again.';
            setError(errorMessage);
        } finally {
            setIsDeleting(false);
            if (!error) {
                setShowConfirm(false);
            }
        }
    };

    const handleClick = () => {
        setShowConfirm(true);
    };

    const handleCancel = () => {
        setShowConfirm(false);
        setError(null);
    };

    if (showConfirm) {
        return (
            <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                        Delete {department.name}?
                    </span>
                    <button
                        onClick={handleDelete}
                        disabled={isDeleting}
                        className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                    >
                        {isDeleting ? 'Deleting...' : 'Yes'}
                    </button>
                    <button
                        onClick={handleCancel}
                        disabled={isDeleting}
                        className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
                    >
                        Cancel
                    </button>
                </div>
                {error && (
                    <div className="text-xs text-red-600 dark:text-red-400 max-w-xs">
                        {error}
                    </div>
                )}
            </div>
        );
    }

    return (
        <button
            onClick={handleClick}
            disabled={isDeleting}
            className={`
                text-red-700 dark:text-red-400 
                hover:text-red-800 dark:hover:text-red-300 
                disabled:opacity-50
                transition-all duration-200
                ${isDeleting ? 'animate-spin' : ''}
            `}
            title={`Delete ${department.name}`}
        >
            <Trash2 size={16} />
        </button>
    );
}
