export interface Module {
    id: number;
    name: string;
    created_at?: string;
    updated_at?: string;
}

export interface CreateModuleRequest {
    name: string;
}

export interface ModuleErrorResponse {
    message: string;
}

export interface ModuleResponse {
    modules: Module[];
    pagination?: {
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
}
