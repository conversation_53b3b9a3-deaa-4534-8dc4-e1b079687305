<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Api\Main\Group;
use App\Models\Api\Main\TimeTable;
use App\Models\Api\Main\Day;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Find all groups that don't have timetables
        $groupsWithoutTimetables = Group::whereDoesntHave('timeTable')->get();

        foreach ($groupsWithoutTimetables as $group) {
            // Create a time table for the group using polymorphic relationship
            $timeTable = $group->timeTable()->create([]);

            // Create days for the time table (Monday to Sunday)
            $dayNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
            foreach ($dayNames as $dayName) {
                Day::create([
                    'name' => $dayName,
                    'time_table_id' => $timeTable->id,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration creates timetables for existing groups
        // Reversing it would be destructive, so we'll leave it empty
        // If you need to reverse, manually delete the timetables
    }
};
