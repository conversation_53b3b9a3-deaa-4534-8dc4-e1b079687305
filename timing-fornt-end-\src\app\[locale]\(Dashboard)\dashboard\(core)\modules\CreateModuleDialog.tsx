"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateModuleForm from "@/lib/ui/forms/module/CreateModuleForm";
import { Plus } from "lucide-react";

export default function CreateModuleDialog() {
    const [open, setOpen] = useState(false);
    const router = useRouter();

    const handleSuccess = () => {
        // Add a small delay to ensure server-side revalidation completes
        setTimeout(() => {
            setOpen(false);
            router.refresh();
        }, 100);
    };

    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Module
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Module">
                <CreateModuleForm onSuccess={handleSuccess} />
            </Dialog>
        </>
    );
}
