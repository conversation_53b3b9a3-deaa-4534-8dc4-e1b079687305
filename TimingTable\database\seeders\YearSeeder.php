<?php

namespace Database\Seeders;

use App\Models\Api\Main\Year;
use App\Models\Api\Core\Department;
use Illuminate\Database\Seeder;

class YearSeeder extends Seeder
{
    public function run()
    {
        $department = Department::first();

        $years = [
            ['year' => 1, 'name' => 'L1'],
            ['year' => 2, 'name' => 'L2 Computer Science'],
            ['year' => 2, 'name' => 'L2 Math'],
            ['year' => 3, 'name' => 'L3 SI'],
            ['year' => 3, 'name' => 'L3 ISIL'],
            ['year' => 4, 'name' => 'M1 AD'],
            ['year' => 4, 'name' => 'M1 IA'],
            ['year' => 5, 'name' => 'M2 AD'],
            ['year' => 5, 'name' => 'M2 IA'],
        ];

        foreach ($years as $year) {
            Year::create([
                'year' => $year['year'],
                'name' => $year['name'],
                'department_id' => $department->id,
            ]);
        }
    }
}
