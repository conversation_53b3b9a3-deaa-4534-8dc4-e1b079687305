'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'
import { CreateStudentRequest, Student, StudentErrorResponse } from '../../types/student/student'

export async function createStudent(studentData: CreateStudentRequest): Promise<Student | StudentErrorResponse> {
    try {
        console.log('Sending student data to API:', studentData);

        const { data } = await axiosInstance.post<{ message: string; student: Student }>(
            `/students`,
            studentData
        )

        console.log('API response:', data);

        revalidatePath('/dashboard')
        revalidatePath('/dashboard/students')
        revalidatePath('/dashboard/groups')
        return data.student
    } catch (error: any) {
        console.error('Error creating student:', error.response?.data || error.message)

        if (error.response?.status === 422) {
            // Validation error
            return {
                message: error.response.data.message || 'Validation failed',
                errors: error.response.data.errors || {}
            } as StudentErrorResponse
        }

        if (error.response?.data) {
            return error.response.data as StudentErrorResponse
        }

        // Network or other errors
        throw new Error(error.message || 'Failed to create student')
    }
}

export async function updateStudent(id: number, studentData: Partial<CreateStudentRequest>): Promise<Student | StudentErrorResponse> {
    try {
        const { data } = await axiosInstance.put<{ message: string; student: Student }>(
            `/students/${id}`,
            studentData
        )
        revalidatePath('/dashboard')
        revalidatePath('/dashboard/students')
        revalidatePath('/dashboard/groups')
        return data.student
    } catch (error: any) {
        console.error('Error updating student:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as StudentErrorResponse
        }
        throw error
    }
}

export async function deleteStudent(id: number): Promise<{ success: boolean }> {
    try {
        await axiosInstance.delete(`/students/${id}`)
        revalidatePath('/dashboard')
        revalidatePath('/dashboard/students')
        revalidatePath('/dashboard/groups')
        return { success: true }
    } catch (error) {
        console.error('Error deleting student:', error)
        throw error
    }
}

export async function createStudentKey(id: number): Promise<{ success: boolean; key?: string }> {
    try {
        await axiosInstance.post<{ key: string }>(`/students/${id}/generate-key`)
        revalidatePath('/dashboard')
        return { success: true }
    } catch (error: any) {
        console.error('Error creating student key:', error.response?.data)
        throw error
    }
} 