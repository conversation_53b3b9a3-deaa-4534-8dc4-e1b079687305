"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { createDepartment } from "@/lib/server/actions/department/DepartmentActions";
import { CheckCircle2, AlertCircle } from "lucide-react";

const createDepartmentSchema = z.object({
  name: z.string().min(1, "Department name is required"),
});

type CreateDepartmentFormData = z.infer<typeof createDepartmentSchema>;

export default function CreateDepartmentForm({ onSuccess }: { onSuccess?: () => void }) {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CreateDepartmentFormData>({
    resolver: zodResolver(createDepartmentSchema),
  });

  const onSubmit = async (data: CreateDepartmentFormData) => {
    setError(null);
    setSuccess(false);
    try {
      const response = await createDepartment({ name: data.name });
      console.log('Create department response:', response);
      if (response && (response as any).message) {
        setError((response as any).message);
        return;
      }
      setSuccess(true);
      reset();

      // Auto-close dialog after showing success message
      setTimeout(() => {
        onSuccess?.();
      }, 1500);
    } catch (e: any) {
      console.error('Create department error:', e);
      setError(e.message || "Failed to create department");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
      {error && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
          <CheckCircle2 size={20} />
          <span>Department created successfully!</span>
        </div>
      )}
      <label>
        Department Name
        <input type="text" {...register("name")} className="input" />
        {errors.name && <span className="text-red-500">{errors.name.message}</span>}
      </label>
      <Button type="submit" mode="filled" disabled={isSubmitting}>
        {isSubmitting ? "Creating..." : "Create Department"}
      </Button>
    </form>
  );
} 