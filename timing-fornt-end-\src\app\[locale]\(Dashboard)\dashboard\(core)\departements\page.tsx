import Button from "@/lib/ui/components/global/Buttons/Button";
import {
    DashContent,
    DashContentAction,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Pencil, Timer, Trash, UserPen } from "lucide-react";
import Link from "next/link";
import CreateDepartmentDialog from "./CreateDepartmentDialog";
import { getDepartments } from "@/lib/server/actions/department/DepartmentActions";
import DeleteDepartment from "@/lib/ui/forms/department/delete";

type Department = {
    id: number;
    name: string;
};

export default async function DepartmentsPage() {
    const departmentsData = await getDepartments();
    const departments = departmentsData.departments || [];

    return (
        <DashContent>
            <DashContenTitle>Departments</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem title="Total Departments" value={departments.length.toString()} icon={<UserPen size={80} />} />
            </DashContentStat>
            <DashContentAction>
                <CreateDepartmentDialog />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Department Name', 'Settings']} />
                <tbody>
                    {departments.map((department) => (
                        <TableTr key={department.id}>
                            <TableTdMain value={department.name} />
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/departments/${department.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <DeleteDepartment department={department} />
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
        </DashContent>
    );
}
